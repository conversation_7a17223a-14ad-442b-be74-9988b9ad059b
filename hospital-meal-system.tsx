import React, { useState, useEffect } from 'react';
import { User, Settings, Users, ChefHat, FileText, BarChart, Calendar, Package, Shield, LogOut } from 'lucide-react';

// 模拟静态JSON配置文件
const menuConfig = {
  "admin": [
    { id: "dashboard", name: "系统总览", icon: "BarChart", path: "/dashboard" },
    { id: "user-management", name: "用户管理", icon: "Users", path: "/users" },
    { id: "role-management", name: "角色管理", icon: "Shield", path: "/roles" },
    { id: "meal-planning", name: "膳食计划", icon: "Calendar", path: "/meal-plan" },
    { id: "nutrition-analysis", name: "营养分析", icon: "FileText", path: "/nutrition" },
    { id: "inventory", name: "库存管理", icon: "Package", path: "/inventory" },
    { id: "reports", name: "报表统计", icon: "BarChart", path: "/reports" },
    { id: "system-settings", name: "系统设置", icon: "Settings", path: "/settings" }
  ],
  "nutritionist": [
    { id: "dashboard", name: "工作台", icon: "BarChart", path: "/dashboard" },
    { id: "meal-planning", name: "膳食计划", icon: "Calendar", path: "/meal-plan" },
    { id: "nutrition-analysis", name: "营养分析", icon: "FileText", path: "/nutrition" },
    { id: "patient-meals", name: "患者配餐", icon: "ChefHat", path: "/patient-meals" },
    { id: "diet-reports", name: "膳食报告", icon: "FileText", path: "/diet-reports" }
  ],
  "staff": [
    { id: "dashboard", name: "工作台", icon: "BarChart", path: "/dashboard" },
    { id: "meal-orders", name: "餐食订单", icon: "ChefHat", path: "/meal-orders" },
    { id: "inventory-view", name: "库存查看", icon: "Package", path: "/inventory-view" }
  ]
};

const users = [
  { id: 1, username: "admin", password: "admin123", role: "admin", name: "系统管理员", department: "信息科" },
  { id: 2, username: "nutritionist1", password: "nutri123", role: "nutritionist", name: "李营养师", department: "营养科" },
  { id: 3, username: "staff1", password: "staff123", role: "staff", name: "张员工", department: "膳食科" }
];

const roleNames = {
  admin: "系统管理员",
  nutritionist: "营养师",
  staff: "普通员工"
};

const iconComponents = {
  User, Settings, Users, ChefHat, FileText, BarChart, Calendar, Package, Shield
};

function HospitalMealSystem() {
  const [currentUser, setCurrentUser] = useState(null);
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [activeMenu, setActiveMenu] = useState('dashboard');

  // 登录处理
  const handleLogin = () => {
    const user = users.find(u => 
      u.username === loginForm.username && u.password === loginForm.password
    );
    
    if (user) {
      setCurrentUser(user);
      setActiveMenu('dashboard');
    } else {
      alert('用户名或密码错误');
    }
  };

  // 登出处理
  const handleLogout = () => {
    setCurrentUser(null);
    setLoginForm({ username: '', password: '' });
  };

  // 获取当前用户菜单
  const getCurrentUserMenu = () => {
    if (!currentUser) return [];
    return menuConfig[currentUser.role] || [];
  };

  // 登录页面
  if (!currentUser) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md">
          <div className="text-center mb-8">
            <div className="bg-blue-600 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <ChefHat className="text-white w-8 h-8" />
            </div>
            <h1 className="text-2xl font-bold text-gray-800">医院膳食系统</h1>
            <p className="text-gray-600 mt-2">请登录您的账户</p>
          </div>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                用户名
              </label>
              <input
                type="text"
                value={loginForm.username}
                onChange={(e) => setLoginForm({...loginForm, username: e.target.value})}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入用户名"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <input
                type="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({...loginForm, password: e.target.value})}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入密码"
              />
            </div>
            
            <button
              onClick={handleLogin}
              className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-medium transition-colors"
            >
              登录
            </button>
          </div>
          
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium text-gray-800 mb-3">测试账户：</h3>
            <div className="space-y-2 text-sm">
              <div><strong>管理员:</strong> admin / admin123</div>
              <div><strong>营养师:</strong> nutritionist1 / nutri123</div>
              <div><strong>普通员工:</strong> staff1 / staff123</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 主系统界面
  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* 侧边栏 */}
      <div className="bg-white shadow-lg w-64 flex flex-col">
        {/* 用户信息 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-600 w-12 h-12 rounded-full flex items-center justify-center">
              <User className="text-white w-6 h-6" />
            </div>
            <div>
              <div className="font-medium text-gray-800">{currentUser.name}</div>
              <div className="text-sm text-gray-600">{roleNames[currentUser.role]}</div>
              <div className="text-xs text-gray-500">{currentUser.department}</div>
            </div>
          </div>
        </div>
        
        {/* 菜单导航 */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {getCurrentUserMenu().map((item) => {
              const IconComponent = iconComponents[item.icon] || FileText;
              return (
                <button
                  key={item.id}
                  onClick={() => setActiveMenu(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    activeMenu === item.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{item.name}</span>
                </button>
              );
            })}
          </div>
        </nav>
        
        {/* 登出按钮 */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="w-full flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <LogOut className="w-5 h-5" />
            <span>退出登录</span>
          </button>
        </div>
      </div>
      
      {/* 主内容区 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部导航栏 */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-xl font-semibold text-gray-800">
              {getCurrentUserMenu().find(item => item.id === activeMenu)?.name || '系统总览'}
            </h1>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                当前角色: {roleNames[currentUser.role]}
              </span>
            </div>
          </div>
        </header>
        
        {/* 内容区域 */}
        <main className="flex-1 p-6">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <MenuContent activeMenu={activeMenu} userRole={currentUser.role} />
          </div>
        </main>
      </div>
    </div>
  );
}

// 菜单内容组件
function MenuContent({ activeMenu, userRole }) {
  const getContent = () => {
    switch (activeMenu) {
      case 'dashboard':
        return (
          <div>
            <h2 className="text-lg font-semibold mb-4">欢迎使用医院膳食系统</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-800">今日配餐</h3>
                <p className="text-2xl font-bold text-blue-600 mt-2">268</p>
                <p className="text-sm text-blue-600">份</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-800">营养达标率</h3>
                <p className="text-2xl font-bold text-green-600 mt-2">95.2%</p>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h3 className="font-medium text-orange-800">待处理订单</h3>
                <p className="text-2xl font-bold text-orange-600 mt-2">12</p>
                <p className="text-sm text-orange-600">个</p>
              </div>
            </div>
          </div>
        );
        
      case 'user-management':
        return (
          <div>
            <h2 className="text-lg font-semibold mb-4">用户管理</h2>
            <p className="text-gray-600 mb-4">管理系统用户账户和权限设置</p>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">用户名</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">姓名</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">角色</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">部门</th>
                    <th className="border border-gray-300 px-4 py-2 text-left">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map(user => (
                    <tr key={user.id}>
                      <td className="border border-gray-300 px-4 py-2">{user.username}</td>
                      <td className="border border-gray-300 px-4 py-2">{user.name}</td>
                      <td className="border border-gray-300 px-4 py-2">{roleNames[user.role]}</td>
                      <td className="border border-gray-300 px-4 py-2">{user.department}</td>
                      <td className="border border-gray-300 px-4 py-2">
                        <button className="text-blue-600 hover:underline mr-2">编辑</button>
                        <button className="text-red-600 hover:underline">删除</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
        
      case 'meal-planning':
        return (
          <div>
            <h2 className="text-lg font-semibold mb-4">膳食计划</h2>
            <p className="text-gray-600 mb-4">制定和管理患者膳食计划</p>
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">本周膳食计划</h3>
                <div className="grid grid-cols-7 gap-2 text-sm">
                  {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map(day => (
                    <div key={day} className="bg-white p-2 rounded text-center">
                      <div className="font-medium">{day}</div>
                      <div className="text-xs text-gray-600 mt-1">已安排</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
        
      default:
        return (
          <div>
            <h2 className="text-lg font-semibold mb-4">功能开发中</h2>
            <p className="text-gray-600">该功能正在开发中，敬请期待...</p>
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                当前用户角色: <strong>{roleNames[userRole]}</strong>
              </p>
              <p className="text-sm text-blue-600 mt-1">
                根据您的权限，显示相应的功能菜单
              </p>
            </div>
          </div>
        );
    }
  };

  return getContent();
}

export default HospitalMealSystem;