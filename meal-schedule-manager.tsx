import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Copy, Plus, ChevronLeft, ChevronRight, BookOpen, Utensils, Upload, Download, Search, Filter, X, Check, AlertCircle } from 'lucide-react';

const HospitalMealManager = () => {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState('week');
  const [showImportModal, setShowImportModal] = useState(false);
  const [showDictModal, setShowDictModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [selectedMealType, setSelectedMealType] = useState('breakfast');
  const [selectedMealDate, setSelectedMealDate] = useState(null);

  // 膳食字典库
  const [dietDictionary, setDietDictionary] = useState([
    // 主食类
    { id: 1, name: "白米粥", category: "主食", type: "粥类", nutrition: "易消化", allergens: [], calories: 120, price: 2.0, suitable: ["普通", "流质"] },
    { id: 2, name: "小米粥", category: "主食", type: "粥类", nutrition: "养胃", allergens: [], calories: 135, price: 2.5, suitable: ["普通", "流质"] },
    { id: 3, name: "蒸蛋羹", category: "主食", type: "蛋类", nutrition: "高蛋白", allergens: ["蛋类"], calories: 85, price: 3.0, suitable: ["普通", "软食"] },
    { id: 4, name: "白面条", category: "主食", type: "面食", nutrition: "易消化", allergens: ["麸质"], calories: 220, price: 4.0, suitable: ["普通", "软食"] },
    
    // 蔬菜类
    { id: 5, name: "清炒小白菜", category: "蔬菜", type: "绿叶菜", nutrition: "维生素C", allergens: [], calories: 45, price: 3.5, suitable: ["普通", "素食"] },
    { id: 6, name: "胡萝卜丝", category: "蔬菜", type: "根茎类", nutrition: "维生素A", allergens: [], calories: 35, price: 3.0, suitable: ["普通", "素食"] },
    { id: 7, name: "冬瓜汤", category: "蔬菜", type: "汤类", nutrition: "利尿", allergens: [], calories: 25, price: 2.5, suitable: ["普通", "流质"] },
    
    // 肉类
    { id: 8, name: "清蒸鸡胸肉", category: "肉类", type: "禽肉", nutrition: "高蛋白低脂", allergens: [], calories: 165, price: 8.0, suitable: ["普通", "高蛋白"] },
    { id: 9, name: "红烧肉丁", category: "肉类", type: "猪肉", nutrition: "蛋白质", allergens: [], calories: 280, price: 10.0, suitable: ["普通"] },
    { id: 10, name: "清炖鱼片", category: "肉类", type: "鱼类", nutrition: "优质蛋白", allergens: ["鱼类"], calories: 140, price: 12.0, suitable: ["普通", "高蛋白"] },
    
    // 汤类
    { id: 11, name: "银耳莲子汤", category: "汤品", type: "甜汤", nutrition: "润燥", allergens: [], calories: 80, price: 4.0, suitable: ["普通", "流质"] },
    { id: 12, name: "紫菜蛋花汤", category: "汤品", type: "咸汤", nutrition: "碘元素", allergens: ["蛋类"], calories: 35, price: 3.5, suitable: ["普通", "流质"] },
    
    // 特殊膳食
    { id: 13, name: "糖尿病餐A", category: "特殊", type: "糖尿病", nutrition: "低糖", allergens: [], calories: 200, price: 15.0, suitable: ["糖尿病"] },
    { id: 14, name: "高血压餐A", category: "特殊", type: "高血压", nutrition: "低盐", allergens: [], calories: 180, price: 15.0, suitable: ["高血压"] },
    { id: 15, name: "流质餐A", category: "特殊", type: "流质", nutrition: "易吸收", allergens: [], calories: 150, price: 12.0, suitable: ["流质"] },
  ]);

  // 一周膳食安排
  const [weeklySchedule, setWeeklySchedule] = useState({});

  // 膳食类型配置
  const mealTypes = [
    { key: 'breakfast', name: '早餐', time: '07:00-09:00', color: 'bg-orange-100 text-orange-800 border-orange-200', icon: '🌅' },
    { key: 'lunch', name: '午餐', time: '11:30-13:30', color: 'bg-blue-100 text-blue-800 border-blue-200', icon: '☀️' },
    { key: 'dinner', name: '晚餐', time: '17:30-19:30', color: 'bg-purple-100 text-purple-800 border-purple-200', icon: '🌙' }
  ];

  const categories = ['all', '主食', '蔬菜', '肉类', '汤品', '特殊'];

  // 获取当前周的日期
  const getWeekDates = () => {
    const startOfWeek = new Date(currentWeek);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  // 格式化日期
  const formatDate = (date) => {
    return date.toISOString().split('T')[0];
  };

  // 获取过滤后的膳食字典
  const getFilteredDietItems = () => {
    return dietDictionary.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.nutrition.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = filterCategory === 'all' || item.category === filterCategory;
      return matchesSearch && matchesCategory;
    });
  };

  // 添加膳食到计划
  const addMealToSchedule = (date, mealType, dietItem) => {
    const dateStr = formatDate(date);
    const newSchedule = { ...weeklySchedule };
    
    if (!newSchedule[dateStr]) {
      newSchedule[dateStr] = {};
    }
    if (!newSchedule[dateStr][mealType]) {
      newSchedule[dateStr][mealType] = [];
    }
    
    const exists = newSchedule[dateStr][mealType].some(item => item.id === dietItem.id);
    if (!exists) {
      newSchedule[dateStr][mealType].push(dietItem);
      setWeeklySchedule(newSchedule);
    }
  };

  // 移除膳食
  const removeMealFromSchedule = (date, mealType, itemId) => {
    const dateStr = formatDate(date);
    const newSchedule = { ...weeklySchedule };
    
    if (newSchedule[dateStr] && newSchedule[dateStr][mealType]) {
      newSchedule[dateStr][mealType] = newSchedule[dateStr][mealType].filter(item => item.id !== itemId);
      setWeeklySchedule(newSchedule);
    }
  };

  // 批量导入历史食谱
  const importHistoricalMeals = (targetWeek, sourceData) => {
    const newSchedule = { ...weeklySchedule };
    Object.keys(sourceData).forEach(date => {
      newSchedule[date] = { ...sourceData[date] };
    });
    setWeeklySchedule(newSchedule);
    setShowImportModal(false);
  };

  // 导出当前周食谱
  const exportWeeklyMenu = () => {
    const weekDates = getWeekDates();
    const menuData = {
      weekStart: formatDate(weekDates[0]),
      weekEnd: formatDate(weekDates[6]),
      schedule: {}
    };
    
    weekDates.forEach(date => {
      const dateStr = formatDate(date);
      if (weeklySchedule[dateStr]) {
        menuData.schedule[dateStr] = weeklySchedule[dateStr];
      }
    });
    
    const dataStr = JSON.stringify(menuData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `meal-schedule-${formatDate(weekDates[0])}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  // 周视图
  const WeekView = () => {
    const weekDates = getWeekDates();
    const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    
    return (
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button 
                onClick={() => setCurrentWeek(new Date(currentWeek.getTime() - 7 * 24 * 60 * 60 * 1000))}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              <h2 className="text-xl font-bold text-gray-800">
                {weekDates[0].toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })} - 
                {weekDates[6].toLocaleDateString('zh-CN', { month: 'long', day: 'numeric' })}
              </h2>
              <button 
                onClick={() => setCurrentWeek(new Date(currentWeek.getTime() + 7 * 24 * 60 * 60 * 1000))}
                className="p-2 hover:bg-gray-100 rounded-lg"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowImportModal(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                <Upload className="w-4 h-4" />
                <span>导入历史</span>
              </button>
              <button
                onClick={exportWeeklyMenu}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Download className="w-4 h-4" />
                <span>导出食谱</span>
              </button>
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                  时间
                </th>
                {weekDates.map((date, index) => (
                  <th key={index} className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div>{weekDays[index]}</div>
                    <div className="text-sm font-normal text-gray-400">
                      {date.toLocaleDateString('zh-CN', { month: 'numeric', day: 'numeric' })}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {mealTypes.map(mealType => (
                <tr key={mealType.key} className="hover:bg-gray-50">
                  <td className="px-4 py-6 whitespace-nowrap">
                    <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${mealType.color}`}>
                      <span className="text-lg">{mealType.icon}</span>
                      <div>
                        <div className="font-medium">{mealType.name}</div>
                        <div className="text-xs opacity-80">{mealType.time}</div>
                      </div>
                    </div>
                  </td>
                  {weekDates.map((date, dateIndex) => {
                    const dateStr = formatDate(date);
                    const dayMeals = weeklySchedule[dateStr]?.[mealType.key] || [];
                    
                    return (
                      <td key={dateIndex} className="px-4 py-6 align-top">
                        <div className="min-h-[120px] space-y-2">
                          {dayMeals.map((meal, mealIndex) => (
                            <div 
                              key={mealIndex}
                              className="group relative bg-gray-50 rounded-lg p-2 hover:bg-gray-100 transition-colors"
                            >
                              <button
                                onClick={() => removeMealFromSchedule(date, mealType.key, meal.id)}
                                className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                              >
                                <X className="w-3 h-3" />
                              </button>
                              <div className="text-sm font-medium text-gray-800 pr-4">
                                {meal.name}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {meal.category} • {meal.calories}kcal
                              </div>
                              {meal.allergens.length > 0 && (
                                <div className="flex items-center mt-1">
                                  <AlertCircle className="w-3 h-3 text-orange-500 mr-1" />
                                  <span className="text-xs text-orange-600">
                                    {meal.allergens.join(', ')}
                                  </span>
                                </div>
                              )}
                            </div>
                          ))}
                          <button
                            onClick={() => {
                              setSelectedMealDate(date);
                              setSelectedMealType(mealType.key);
                              setShowDictModal(true);
                            }}
                            className="w-full py-2 border-2 border-dashed border-gray-300 rounded-lg text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors"
                          >
                            <Plus className="w-4 h-4 mx-auto" />
                          </button>
                        </div>
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // 膳食字典模态框
  const DietDictionaryModal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-4/5 max-w-4xl h-4/5 max-h-[600px] flex flex-col">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold">膳食字典库</h3>
            <button
              onClick={() => setShowDictModal(false)}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="flex space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索膳食名称或营养成分..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg"
              />
            </div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg"
            >
              {categories.map(cat => (
                <option key={cat} value={cat}>
                  {cat === 'all' ? '全部分类' : cat}
                </option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {getFilteredDietItems().map(item => (
              <div 
                key={item.id}
                onClick={() => {
                  if (selectedMealDate && selectedMealType) {
                    addMealToSchedule(selectedMealDate, selectedMealType, item);
                    setShowDictModal(false);
                  }
                }}
                className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md cursor-pointer transition-all"
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-800">{item.name}</h4>
                  <span className="text-sm text-green-600 font-medium">¥{item.price}</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {item.category}
                    </span>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      {item.type}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <div>营养: {item.nutrition}</div>
                    <div>热量: {item.calories}kcal</div>
                  </div>
                  
                  {item.allergens.length > 0 && (
                    <div className="flex items-center text-xs text-orange-600">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      过敏原: {item.allergens.join(', ')}
                    </div>
                  )}
                  
                  <div className="flex flex-wrap gap-1 mt-2">
                    {item.suitable.map(type => (
                      <span key={type} className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        {type}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  // 历史导入模态框
  const ImportModal = () => {
    const [importData, setImportData] = useState('');
    
    const handleImport = () => {
      try {
        const data = JSON.parse(importData);
        if (data.schedule) {
          importHistoricalMeals(currentWeek, data.schedule);
        }
      } catch (error) {
        alert('导入数据格式错误，请检查JSON格式');
      }
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-2/3 max-w-2xl">
          <h3 className="text-lg font-bold mb-4">导入历史食谱</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                粘贴JSON格式的食谱数据
              </label>
              <textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder='{"schedule": {"2025-06-01": {"breakfast": [...], "lunch": [...], "dinner": [...]}}}'
                className="w-full h-40 p-3 border border-gray-300 rounded-lg text-sm font-mono"
              />
            </div>
            
            <div className="text-sm text-gray-500">
              <p>支持的格式示例：</p>
              <code className="block mt-1 p-2 bg-gray-100 rounded text-xs">
                {`{"schedule": {"2025-06-01": {"breakfast": [{"id": 1, "name": "白米粥", ...}]}}}`}
              </code>
            </div>
          </div>
          
          <div className="flex space-x-3 mt-6">
            <button
              onClick={() => setShowImportModal(false)}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleImport}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              导入
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 统计信息
  const WeeklyStats = () => {
    const weekDates = getWeekDates();
    let totalMeals = 0;
    let totalCalories = 0;
    let totalCost = 0;
    const allergenCount = {};
    
    weekDates.forEach(date => {
      const dateStr = formatDate(date);
      const daySchedule = weeklySchedule[dateStr];
      if (daySchedule) {
        Object.values(daySchedule).forEach(mealList => {
          mealList.forEach(meal => {
            totalMeals++;
            totalCalories += meal.calories || 0;
            totalCost += meal.price || 0;
            meal.allergens?.forEach(allergen => {
              allergenCount[allergen] = (allergenCount[allergen] || 0) + 1;
            });
          });
        });
      }
    });
    
    return (
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h3 className="text-lg font-semibold mb-4">本周统计</h3>
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{totalMeals}</div>
            <div className="text-sm text-blue-600">总膳食数</div>
          </div>
          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{totalCalories}</div>
            <div className="text-sm text-green-600">总热量(kcal)</div>
          </div>
          <div className="text-center p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">¥{totalCost.toFixed(1)}</div>
            <div className="text-sm text-purple-600">预估成本</div>
          </div>
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{Object.keys(allergenCount).length}</div>
            <div className="text-sm text-orange-600">过敏原种类</div>
          </div>
        </div>
        
        {Object.keys(allergenCount).length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">过敏原统计</h4>
            <div className="flex flex-wrap gap-2">
              {Object.entries(allergenCount).map(([allergen, count]) => (
                <span key={allergen} className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                  {allergen}: {count}次
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">医院食堂膳食排期管理</h1>
              <p className="text-gray-600 mt-1">科学配餐，营养均衡，关爱患者健康</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 text-sm text-gray-500">
                <Clock className="w-4 h-4" />
                <span>最后更新: {new Date().toLocaleString('zh-CN')}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <WeekView />
          </div>
          
          <div className="space-y-6">
            <WeeklyStats />
            
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <BookOpen className="w-5 h-5 mr-2" />
                膳食字典库
              </h3>
              <div className="text-sm text-gray-600 mb-4">
                共 {dietDictionary.length} 种膳食可选
              </div>
              <div className="space-y-2">
                {categories.slice(1).map(category => {
                  const count = dietDictionary.filter(item => item.category === category).length;
                  return (
                    <div key={category} className="flex justify-between text-sm">
                      <span className="text-gray-600">{category}:</span>
                      <span className="font-medium">{count} 种</span>
                    </div>
                  );
                })}
              </div>
              <button
                onClick={() => setShowDictModal(true)}
                className="w-full mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                浏览膳食库
              </button>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4">快速操作</h3>
              <div className="space-y-2">
                <button
                  onClick={() => setShowImportModal(true)}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                >
                  <Upload className="w-4 h-4" />
                  <span>导入历史食谱</span>
                </button>
                <button
                  onClick={exportWeeklyMenu}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <Download className="w-4 h-4" />
                  <span>导出本周食谱</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {showDictModal && <DietDictionaryModal />}
      {showImportModal && <ImportModal />}
    </div>
  );
};

export default HospitalMealManager;