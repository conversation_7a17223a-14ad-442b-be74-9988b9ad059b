import React, { useState, useEffect } from 'react';
import { Search, FileText, Printer, Calendar, Tag, ArrowLeft } from 'lucide-react';

// Mock.js 模拟数据
const mockData = [
  {
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '001',
    bingrenid: 'P001',
    bingrenxm: '张三',
    ssid: 'F001',
    name: '糖尿病套餐',
    category: '治疗膳食',
    shuliang: 1,
    price: 28.5,
    date: '2025-06-08',
    canci: '早餐',
    beizhu: ''
  },
  {
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '001',
    bingrenid: 'P001',
    bingrenxm: '张三',
    ssid: 'F002',
    name: '清淡粥类',
    category: '治疗膳食',
    shuliang: 1,
    price: 15.0,
    date: '2025-06-08',
    canci: '午餐',
    beizhu: ''
  },
  {
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '002',
    bingrenid: 'P002',
    bingrenxm: '李四',
    ssid: 'F003',
    name: '普通套餐',
    category: '普通膳食',
    shuliang: 1,
    price: 25.0,
    date: '2025-06-08',
    canci: '早餐',
    beizhu: ''
  },
  {
    bingquid: 'ICU001',
    bingqumc: 'ICU重症监护病区',
    chuanghao: '002',
    bingrenid: 'P002',
    bingrenxm: '李四',
    ssid: 'F004',
    name: '蒸蛋羹',
    category: '普通膳食',
    shuliang: 1,
    price: 8.0,
    date: '2025-06-08',
    canci: '午餐',
    beizhu: ''
  },
  {
    bingquid: 'NKBQ001',
    bingqumc: '内科病区一',
    chuanghao: '101',
    bingrenid: 'P003',
    bingrenxm: '王五',
    ssid: 'F005',
    name: '低盐套餐',
    category: '治疗膳食',
    shuliang: 1,
    price: 30.0,
    date: '2025-06-08',
    canci: '早餐',
    beizhu: '少盐'
  },
  {
    bingquid: 'NKBQ001',
    bingqumc: '内科病区一',
    chuanghao: '102',
    bingrenid: 'P004',
    bingrenxm: '赵六',
    ssid: 'F006',
    name: '软食套餐',
    category: '治疗膳食',
    shuliang: 1,
    price: 22.0,
    date: '2025-06-08',
    canci: '午餐',
    beizhu: ''
  }
];

const HospitalMealReport = () => {
  const [allData] = useState(mockData);
  const [filteredData, setFilteredData] = useState([]);
  const [printMode, setPrintMode] = useState('report'); // 'report' 或 'labels'
  const [showLabelPreview, setShowLabelPreview] = useState(false);
  const [filters, setFilters] = useState({
    bingqu: '',
    date: '2025-06-08',
    canci: '',
    category: ''
  });

  // 获取病区选项
  const bingquOptions = [...new Set(allData.map(item => item.bingqumc))];
  const canciOptions = ['早餐', '午餐', '晚餐'];
  const categoryOptions = [...new Set(allData.map(item => item.category))];

  // 过滤数据
  useEffect(() => {
    let filtered = allData.filter(item => {
      return (!filters.bingqu || item.bingqumc === filters.bingqu) &&
             (!filters.date || item.date === filters.date) &&
             (!filters.canci || item.canci === filters.canci) &&
             (!filters.category || item.category === filters.category);
    });
    setFilteredData(filtered);
  }, [filters, allData]);

  // 按病区分组数据
  const groupDataByBingqu = (data) => {
    const grouped = {};
    data.forEach(item => {
      if (!grouped[item.bingqumc]) {
        grouped[item.bingqumc] = [];
      }
      grouped[item.bingqumc].push(item);
    });
    return grouped;
  };

  // 按床号和患者分组订餐明细
  const groupMealsByPatient = (data) => {
    const grouped = {};
    data.forEach(item => {
      const key = `${item.chuanghao}-${item.bingrenxm}`;
      if (!grouped[key]) {
        grouped[key] = {
          chuanghao: item.chuanghao,
          bingrenxm: item.bingrenxm,
          meals: {}
        };
      }
      if (!grouped[key].meals[item.canci]) {
        grouped[key].meals[item.canci] = [];
      }
      grouped[key].meals[item.canci].push(`${item.name}×${item.shuliang}`);
    });
    return grouped;
  };

  // 统计膳食分类汇总
  const getCategorySummary = (data) => {
    const summary = {};
    data.forEach(item => {
      if (!summary[item.category]) {
        summary[item.category] = {};
      }
      if (!summary[item.category][item.name]) {
        summary[item.category][item.name] = 0;
      }
      summary[item.category][item.name] += item.shuliang;
    });
    return summary;
  };

  const handlePrint = () => {
    window.print();
  };

  const handlePrintLabels = () => {
    setShowLabelPreview(true);
  };

  const handleActualPrint = () => {
    setPrintMode('labels');
    setTimeout(() => {
      window.print();
      setPrintMode('report');
    }, 100);
  };

  const handleBackToReport = () => {
    setShowLabelPreview(false);
  };

  // 按病人分组数据生成标签
  const generatePatientLabels = (data) => {
    const patientMap = {};
    data.forEach(item => {
      const key = `${item.bingrenid}-${item.date}`;
      if (!patientMap[key]) {
        patientMap[key] = {
          bingqumc: item.bingqumc,
          chuanghao: item.chuanghao,
          bingrenxm: item.bingrenxm,
          date: item.date,
          meals: {}
        };
      }
      if (!patientMap[key].meals[item.canci]) {
        patientMap[key].meals[item.canci] = [];
      }
      patientMap[key].meals[item.canci].push({
        name: item.name,
        shuliang: item.shuliang,
        category: item.category,
        beizhu: item.beizhu
      });
    });
    return Object.values(patientMap);
  };

  const groupedData = groupDataByBingqu(filteredData);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 查询条件区域 */}
      <div className={`bg-white p-6 shadow-sm border-b ${showLabelPreview ? 'print:hidden' : 'print:hidden'}`}>
        <h1 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
          <FileText className="mr-2 text-blue-600" />
          医院膳食系统送餐报表
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">病区</label>
            <select
              value={filters.bingqu}
              onChange={(e) => setFilters({...filters, bingqu: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部病区</option>
              {bingquOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">日期</label>
            <input
              type="date"
              value={filters.date}
              onChange={(e) => setFilters({...filters, date: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">餐次</label>
            <select
              value={filters.canci}
              onChange={(e) => setFilters({...filters, canci: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部餐次</option>
              {canciOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">膳食分类</label>
            <select
              value={filters.category}
              onChange={(e) => setFilters({...filters, category: e.target.value})}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">全部分类</option>
              {categoryOptions.map(option => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-600">
            共找到 {filteredData.length} 条记录，{Object.keys(groupedData).length} 个病区
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handlePrint}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <FileText className="mr-2 h-4 w-4" />
              打印报表
            </button>
            <button
              onClick={handlePrintLabels}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Tag className="mr-2 h-4 w-4" />
              打印标签
            </button>
          </div>
        </div>
      </div>

      {/* 标签预览界面 */}
      {showLabelPreview && (
        <div className="bg-white p-6 shadow-sm border-b print:hidden">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <button
                onClick={handleBackToReport}
                className="flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors mr-4"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回报表
              </button>
              <h2 className="text-xl font-bold text-gray-800">标签预览</h2>
            </div>
            <div className="flex space-x-2">
              <div className="text-sm text-gray-600 flex items-center">
                共 {generatePatientLabels(filteredData).length} 个标签
              </div>
              <button
                onClick={handleActualPrint}
                className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
              >
                <Printer className="mr-2 h-4 w-4" />
                确认打印
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 报表内容区域 */}
      <div className="p-6 print:p-0">
        {showLabelPreview || printMode === 'labels' ? (
          // 标签打印模式
          <div className="labels-container">
            <style>
              {`
                @media print {
                  .labels-container {
                    display: flex;
                    flex-wrap: wrap;
                    margin: 0;
                    padding: 0;
                  }
                  .meal-label {
                    width: 5in;
                    height: 3in;
                    border: 1px solid #000;
                    margin: 0;
                    padding: 8px;
                    box-sizing: border-box;
                    page-break-inside: avoid;
                    break-inside: avoid;
                    display: flex;
                    flex-direction: column;
                    font-size: 12px;
                    line-height: 1.3;
                  }
                  .label-header {
                    text-align: center;
                    border-bottom: 1px solid #000;
                    padding-bottom: 4px;
                    margin-bottom: 6px;
                  }
                  .label-title {
                    font-size: 16px;
                    font-weight: bold;
                    margin-bottom: 2px;
                  }
                  .label-info {
                    font-size: 11px;
                  }
                  .label-content {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                  }
                  .meal-section {
                    margin-bottom: 4px;
                  }
                  .meal-title {
                    font-weight: bold;
                    font-size: 13px;
                    margin-bottom: 2px;
                  }
                  .meal-items {
                    font-size: 11px;
                    padding-left: 8px;
                  }
                  .label-footer {
                    border-top: 1px solid #000;
                    padding-top: 4px;
                    margin-top: auto;
                    font-size: 10px;
                    display: flex;
                    justify-content: space-between;
                  }
                }
                @media screen {
                  .labels-container {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 16px;
                  }
                  .meal-label {
                    border: 2px solid #e5e7eb;
                    border-radius: 8px;
                    padding: 16px;
                    background: white;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                  }
                  .label-header {
                    border-bottom: 2px solid #d1d5db;
                    padding-bottom: 8px;
                    margin-bottom: 12px;
                  }
                  .label-title {
                    font-size: 18px;
                    color: #1f2937;
                  }
                  .meal-title {
                    color: #374151;
                    font-size: 14px;
                  }
                  .label-footer {
                    border-top: 1px solid #d1d5db;
                    padding-top: 8px;
                    margin-top: 12px;
                    color: #6b7280;
                  }
                }
              `}
            </style>
            {generatePatientLabels(filteredData).map((patient, index) => (
              <div key={index} className="meal-label">
                <div className="label-header">
                  <div className="label-title">送餐标签</div>
                  <div className="label-info">
                    {patient.bingqumc} | {patient.chuanghao}床 | {patient.date}
                  </div>
                </div>
                
                <div className="label-content">
                  <div style={{fontSize: '14px', fontWeight: 'bold', marginBottom: '8px'}}>
                    患者：{patient.bingrenxm}
                  </div>
                  
                  {Object.entries(patient.meals).map(([canci, items]) => (
                    <div key={canci} className="meal-section">
                      <div className="meal-title">{canci}：</div>
                      <div className="meal-items">
                        {items.map((item, idx) => (
                          <div key={idx}>
                            • {item.name} ×{item.shuliang}
                            {item.beizhu && <span style={{color: '#666', fontSize: '10px'}}> ({item.beizhu})</span>}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="label-footer">
                  <span>打印：{new Date().toLocaleString()}</span>
                  <span>标签 #{index + 1}</span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // 原有报表模式
          <>
            {Object.entries(groupedData).map(([bingqumc, bingquData], index) => {
          const patientMeals = groupMealsByPatient(bingquData);
          const categorySummary = getCategorySummary(bingquData);
          
          return (
            <div key={bingqumc} className={`bg-white ${index > 0 ? 'mt-8 print:mt-0 print:break-before-page' : ''}`}>
              {/* 报表抬头 */}
              <div className="border-b-2 border-gray-800 pb-2 mb-3">
                <div className="text-center">
                  <h2 className="text-xl font-bold text-gray-800 mb-1">送餐报表</h2>
                  <div className="text-sm">
                    <span><strong>病区：</strong>{bingqumc}</span>
                    <span className="mx-4"><strong>用餐日期：</strong>{filters.date}</span>
                    <span><strong>餐次：</strong>{filters.canci || '全部'}</span>
                    {filters.category && <span className="ml-4"><strong>膳食分类：</strong>{filters.category}</span>}
                  </div>
                </div>
              </div>

              {/* 报表表格 */}
              <div className="mb-6">
                <table className="w-full border-collapse border border-gray-400 text-sm">
                  <thead>
                    <tr className="bg-gray-100">
                      <th className="border border-gray-400 px-2 py-1 text-left w-16">床号</th>
                      <th className="border border-gray-400 px-2 py-1 text-left w-20">姓名</th>
                      <th className="border border-gray-400 px-2 py-1 text-left">订餐明细</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Object.values(patientMeals).map((patient, idx) => (
                      <tr key={idx} className="hover:bg-gray-50">
                        <td className="border border-gray-400 px-2 py-1 text-center">{patient.chuanghao}</td>
                        <td className="border border-gray-400 px-2 py-1">{patient.bingrenxm}</td>
                        <td className="border border-gray-400 px-2 py-1">
                          {Object.entries(patient.meals).map(([canci, items]) => (
                            <div key={canci} className="mb-1">
                              <strong>{canci}：</strong>[{items.join('，')}]
                            </div>
                          ))}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* 膳食分类汇总 */}
              <div className="border-t-2 border-gray-300 pt-3">
                <h3 className="font-bold text-gray-800 mb-2">膳食分类汇总：</h3>
                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(categorySummary).map(([category, items]) => (
                    <div key={category} className="text-sm">
                      <div className="font-semibold text-gray-700 mb-1">{category}：</div>
                      <div className="ml-4">
                        {Object.entries(items).map(([itemName, count]) => (
                          <div key={itemName} className="flex justify-between">
                            <span>{itemName}</span>
                            <span>{count}份</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              {/* 报表页脚 */}
              <div className="mt-6 pt-3 border-t border-gray-300 text-xs text-gray-500 flex justify-between">
                <span>打印时间：{new Date().toLocaleString()}</span>
                <span>第 {index + 1} 页，共 {Object.keys(groupedData).length} 页</span>
              </div>
            </div>
          );
        })}
        
        {Object.keys(groupedData).length === 0 && (
          <div className="bg-white p-8 text-center text-gray-500">
            <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>未找到符合条件的记录</p>
          </div>
        )}
          </>
        )}
      </div>
    </div>
  );
};

export default HospitalMealReport;